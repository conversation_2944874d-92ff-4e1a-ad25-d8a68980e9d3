declare namespace API {
  /**
   * 统一返回信息格式
   */
  type ResType<T> = {
    errCode: number;
    data?: T;
    msg?: string;
  };

  interface IResponseDate {
    createdAt: Date;
    updatedAt: Date;
  }

  // ==================== 认证相关类型定义 ====================

  /**
   * 登录参数
   */
  interface LoginParams {
    username: string;
    password: string;
  }

  /**
   * 登录响应
   */
  interface LoginResponse {
    token: string;
    user: {
      id: number;
      username: string;
      role: string;
      enabled: boolean;
      createdAt: string;
      updatedAt: string;
    };
  }

  /**
   * 当前用户信息
   */
  interface CurrentUser {
    id: number;
    username: string;
    role: string;
    permissions: string[];
  }

  /**
   * 通用消息响应
   */
  interface MessageResponse {
    message: string;
  }

  // ==================== 用户管理相关类型定义 ====================

  /**
   * 用户信息类型定义
   */
  interface UserInfo {
    id: number;
    username: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  }

  /**
   * 用户列表响应类型
   */
  interface UserListResponse {
    list: UserInfo[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }

  /**
   * 创建用户参数
   */
  interface CreateUserParams {
    username: string;
    password: string;
    role?: string;
    isActive?: boolean;
  }

  /**
   * 更新用户参数
   */
  interface UpdateUserParams {
    username?: string;
    password?: string;
    role?: string;
    isActive?: boolean;
  }

  /**
   * 获取用户列表参数
   */
  interface GetUserListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
  }

  // ==================== 上传相关类型定义 ====================

  /**
   * 上传文件响应类型
   */
  interface UploadFileResponse {
    url: string;
    filename: string;
    size: number;
    photoId?: number; // 当createPhoto=true时返回
  }

  /**
   * 文件信息类型
   */
  interface FileInfo {
    id: number;
    name: string;
    url: string;
    size?: number;
    type?: string;
    createdAt?: string;
    updatedAt?: string;
  }

  /**
   * 照片记录类型
   */
  interface PhotoRecord {
    id: number;
    name: string;
    url: string;
    path?: string;
    mountainId?: number;
    waterSystemId?: number;
    historicalElementId?: number;
    mountain?: { id: number; name: string };
    waterSystem?: { id: number; name: string };
    historicalElement?: { id: number; name: string };
    createdAt: string;
    updatedAt: string;
  }

  /**
   * 照片列表响应类型
   */
  interface PhotoListResponse {
    list: PhotoRecord[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 照片统计响应类型
   */
  interface PhotoStatistics {
    total: number;
    mountainPhotos: number;
    waterSystemPhotos: number;
    historicalElementPhotos: number;
    unassignedPhotos: number;
  }

  /**
   * 上传文件选项
   */
  interface UploadFileOptions {
    photoName?: string;
    entityType?: 'mountain' | 'waterSystem' | 'historicalElement';
    entityId?: number;
  }

  /**
   * 创建照片参数
   */
  interface CreatePhotoParams {
    name: string;
    url: string;
    mountainId?: number;
    waterSystemId?: number;
    historicalElementId?: number;
  }

  /**
   * 更新照片参数
   */
  interface UpdatePhotoParams {
    name?: string;
    url?: string;
    mountainId?: number;
    waterSystemId?: number;
    historicalElementId?: number;
  }

  /**
   * 获取照片列表参数
   */
  interface GetPhotoListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
  }

  // ==================== 历史要素相关类型定义 ====================

  /**
   * 历史要素类型
   */
  interface HistoricalElement {
    id: number;
    name: string;
    code: string;
    typeDictId?: number;
    constructionLongitude: number;
    constructionLatitude: number;
    locationDescription?: string;
    constructionTime?: string;
    historicalRecords?: string;
    regionDictId: number;
    typeDict?: {
      id: number;
      typeName: string;
      typeCode: string;
    };
    regionDict?: {
      id: number;
      regionName: string;
      regionCode: string;
    };
    photos?: Array<{
      id: number;
      name: string;
      url: string;
    }>;
    createdAt?: string;
    updatedAt?: string;
  }

  /**
   * 创建历史要素参数
   */
  interface CreateHistoricalElementParams {
    name: string;
    code: string;
    typeDictId?: number;
    constructionLongitude: number;
    constructionLatitude: number;
    locationDescription?: string;
    constructionTime?: string;
    historicalRecords?: string;
    regionDictId: number;
  }

  /**
   * 更新历史要素参数
   */
  interface UpdateHistoricalElementParams {
    name?: string;
    code?: string;
    typeDictId?: number;
    constructionLongitude?: number;
    constructionLatitude?: number;
    locationDescription?: string;
    constructionTime?: string;
    historicalRecords?: string;
    regionDictId?: number;
  }

  /**
   * 获取历史要素列表参数
   */
  interface GetHistoricalElementListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    regionId?: number;
    typeId?: number;
  }

  /**
   * 历史要素列表响应
   */
  interface HistoricalElementListResponse {
    list: HistoricalElement[];
    total: number;
    page: number;
    pageSize: number;
  }

  /**
   * 批量导入参数
   */
  interface BatchImportParams {
    elements: CreateHistoricalElementParams[];
  }

  /**
   * 模板下载响应
   */
  interface TemplateDownloadResponse {
    downloadUrl: string;
    filename: string;
    description: string;
  }

  /**
   * Excel导入错误信息
   */
  interface ExcelImportError {
    row: number;
    field: string;
    value: any;
    message: string;
  }

  /**
   * Excel导入预览响应
   */
  interface ExcelImportPreviewResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    errors: ExcelImportError[];
    preview: CreateHistoricalElementParams[];
  }

  /**
   * Excel导入响应
   */
  interface ExcelImportResponse {
    success: boolean;
    message: string;
    totalRows: number;
    validRows: number;
    importedCount?: number;
    errors?: ExcelImportError[];
  }

  /**
   * 历史要素统计
   */
  interface HistoricalElementStatistics {
    total: number;
    byType: Array<{
      typeId: number;
      typeName: string;
      count: number;
    }>;
    byRegion: Array<{
      regionId: number;
      regionName: string;
      count: number;
    }>;
    byPeriod: Array<{
      period: string;
      count: number;
    }>;
  }

  /**
   * 时间轴数据
   */
  interface TimelineData {
    year: number;
    count: number;
    elements: Array<{
      id: number;
      name: string;
      type: string;
    }>;
  }

  /**
   * 获取时间轴参数
   */
  interface GetTimelineParams {
    regionId?: number;
  }

  /**
   * 根据建造时间查询参数
   */
  interface GetByConstructionTimeParams {
    startTime?: string;
    endTime?: string;
  }

  /**
   * 获取统计参数
   */
  interface GetStatisticsParams {
    regionId?: number;
    typeId?: number;
  }

  // ==================== 字典管理相关类型定义 ====================

  // 通用字典基础类型
  interface BaseDictionary {
    id: number;
    status: number;
    sort: number;
    parentId?: number | null;
    createdAt?: string;
    updatedAt?: string;
  }

  // 区域字典类型
  interface RegionDictionary extends BaseDictionary {
    regionCode: string;
    regionName: string;
    regionDesc?: string;
    parent?: RegionDictionary | null;
    children?: RegionDictionary[];
  }

  // 类型字典类型
  interface TypeDictionary extends BaseDictionary {
    typeCode: string;
    typeName: string;
    typeDesc?: string;
    parent?: TypeDictionary | null;
    children?: TypeDictionary[];
  }

  // 关系字典类型
  interface RelationshipDictionary extends BaseDictionary {
    relationCode: string;
    relationName: string;
    relationDesc?: string;
    parent?: RelationshipDictionary | null;
    children?: RelationshipDictionary[];
  }

  // 字典列表查询参数
  interface DictionaryListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
    parentId?: number;
  }

  // 字典列表响应类型
  interface DictionaryListResponse<T> {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
  }

  // 批量状态更新参数
  interface BatchStatusUpdateParams {
    ids: number[];
    status: number;
  }

  // 状态切换响应
  interface StatusToggleResponse {
    status: number;
    message: string;
  }

  // 区域字典数据类型
  interface RegionDict {
    id: number;
    regionCode: string;
    regionName: string;
    parentId?: number | null;
    status: number;
    sort: number;
    regionDesc?: string;
    createdAt?: string;
    updatedAt?: string;
    parent?: RegionDict | null;
    children?: RegionDict[];
  }

  // 区域字典创建参数
  interface CreateRegionDictParams {
    regionCode: string;
    regionName: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    regionDesc?: string;
  }

  // 区域字典更新参数
  interface UpdateRegionDictParams {
    regionCode?: string;
    regionName?: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    regionDesc?: string;
  }

  // 区域字典列表查询参数
  interface GetRegionDictListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
    parentId?: number;
  }

  // 区域字典列表响应
  interface RegionDictListResponse {
    list: RegionDict[];
    total: number;
    page: number;
    pageSize: number;
  }

  // 类型字典数据类型
  interface TypeDict {
    id: number;
    typeCode: string;
    typeName: string;
    parentId?: number | null;
    status: number;
    sort: number;
    typeDesc?: string;
    createdAt?: string;
    updatedAt?: string;
    parent?: TypeDict | null;
    children?: TypeDict[];
  }

  // 类型字典创建参数
  interface CreateTypeDictParams {
    typeCode: string;
    typeName: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    typeDesc?: string;
  }

  // 类型字典更新参数
  interface UpdateTypeDictParams {
    typeCode?: string;
    typeName?: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    typeDesc?: string;
  }

  // 类型字典列表查询参数
  interface GetTypeDictListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
    parentId?: number;
  }

  // 类型字典列表响应
  interface TypeDictListResponse {
    list: TypeDict[];
    total: number;
    page: number;
    pageSize: number;
  }

  // 关系字典数据类型
  interface RelationshipDict {
    id: number;
    relationCode: string;
    relationName: string;
    parentId?: number | null;
    status: number;
    sort: number;
    relationDesc?: string;
    createdAt?: string;
    updatedAt?: string;
    parent?: RelationshipDict | null;
    children?: RelationshipDict[];
  }

  // 关系字典创建参数
  interface CreateRelationshipDictParams {
    relationCode: string;
    relationName: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    relationDesc?: string;
  }

  // 关系字典更新参数
  interface UpdateRelationshipDictParams {
    relationCode?: string;
    relationName?: string;
    parentId?: number | null;
    status?: number;
    sort?: number;
    relationDesc?: string;
  }

  // 关系字典列表查询参数
  interface GetRelationshipDictListParams {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
    parentId?: number;
  }

  // 关系字典列表响应
  interface RelationshipDictListResponse {
    list: RelationshipDict[];
    total: number;
    page: number;
    pageSize: number;
  }
}
