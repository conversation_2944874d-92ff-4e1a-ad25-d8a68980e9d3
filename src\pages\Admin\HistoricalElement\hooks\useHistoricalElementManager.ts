// 使用API命名空间中的类型定义
import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useHistoricalElementData } from './useHistoricalElementData';
import { useHistoricalElementFilters } from './useHistoricalElementFilters';
import { useHistoricalElementOperations } from './useHistoricalElementOperations';

export interface UseHistoricalElementManagerReturn {
  // 数据状态
  data: API.HistoricalElement[];
  statistics: any;
  timelineData: any[];

  // 加载状态
  loading: boolean;
  statisticsLoading: boolean;
  timelineLoading: boolean;
  operationLoading: boolean;
  batchLoading: boolean;

  // 分页状态
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };

  // 筛选状态
  searchKeyword: string;
  regionFilter: number | undefined;
  typeFilter: number | undefined;
  selectedRowKeys: React.Key[];

  // 表单状态
  modalVisible: boolean;
  editingItem: API.HistoricalElement | null;
  importModalVisible: boolean;

  // 标签页状态
  activeTab: string;

  // 操作函数
  handleAdd: () => void;
  handleEdit: (record: API.HistoricalElement) => void;
  handleDelete: (id: number) => void;
  handleSubmit: (values: any) => void;
  handleModalCancel: () => void;

  // 搜索筛选函数
  handleSearch: (value: string) => void;
  handleRegionFilterChange: (value: number | undefined) => void;
  handleTypeFilterChange: (value: number | undefined) => void;
  handleRefresh: () => void;
  handleReset: () => void;

  // 批量操作函数
  handleSelectChange: (newSelectedRowKeys: React.Key[]) => void;
  clearSelection: () => void;
  handleBatchDelete: () => void;
  handleImportModalOpen: () => void;
  handleImportModalClose: () => void;
  handleBatchImport: (importData: string) => void;
  handleDownloadTemplate: () => void;
  handlePreviewExcel: (file: File) => Promise<{
    success: boolean;
    data?: API.ExcelImportPreviewResponse;
    message?: string;
  }>;
  handleImportExcel: (file: File) => Promise<{
    success: boolean;
    data?: API.ExcelImportResponse;
    message?: string;
  }>;

  // 分页函数
  handlePaginationChange: (page: number, pageSize: number) => void;
  handleShowSizeChange: (current: number, size: number) => void;

  // 标签页函数
  handleTabChange: (key: string) => void;
}

export const useHistoricalElementManager =
  (): UseHistoricalElementManagerReturn => {
    // 使用各个子Hook
    const dataHook = useHistoricalElementData();
    const filtersHook = useHistoricalElementFilters();
    const operationsHook = useHistoricalElementOperations();

    // 表单状态
    const [modalVisible, setModalVisible] = useState(false);
    const [editingItem, setEditingItem] =
      useState<API.HistoricalElement | null>(null);
    const [importModalVisible, setImportModalVisible] = useState(false);

    // 标签页状态
    const [activeTab, setActiveTab] = useState('list');

    // 初始化数据
    useEffect(() => {
      dataHook.fetchData(1, 10);
    }, []);

    // 当标签页切换时获取对应数据
    useEffect(() => {
      if (activeTab === 'statistics') {
        dataHook.fetchStatistics(
          filtersHook.regionFilter,
          filtersHook.typeFilter,
        );
      } else if (activeTab === 'timeline') {
        dataHook.fetchTimelineData(filtersHook.regionFilter);
      }
    }, [activeTab]);

    // 当筛选条件变化时重新获取数据
    useEffect(() => {
      const { searchKeyword, regionFilter, typeFilter } = filtersHook;

      if (activeTab === 'list') {
        dataHook.fetchData(
          1,
          dataHook.pagination.pageSize,
          searchKeyword,
          regionFilter,
          typeFilter,
        );
      } else if (activeTab === 'statistics') {
        dataHook.fetchStatistics(regionFilter, typeFilter);
      } else if (activeTab === 'timeline') {
        dataHook.fetchTimelineData(regionFilter);
      }
    }, [
      filtersHook.searchKeyword,
      filtersHook.regionFilter,
      filtersHook.typeFilter,
    ]);

    // 表单操作
    const handleAdd = useCallback(() => {
      setEditingItem(null);
      setModalVisible(true);
    }, []);

    const handleEdit = useCallback((record: API.HistoricalElement) => {
      setEditingItem(record);
      setModalVisible(true);
    }, []);

    const handleDelete = useCallback(
      async (id: number) => {
        const result = await operationsHook.deleteElement(id);
        if (result.success) {
          const { searchKeyword, regionFilter, typeFilter } = filtersHook;
          dataHook.fetchData(
            dataHook.pagination.current,
            dataHook.pagination.pageSize,
            searchKeyword,
            regionFilter,
            typeFilter,
          );
        }
      },
      [operationsHook, filtersHook, dataHook],
    );

    const handleSubmit = useCallback(
      async (values: any) => {
        let result;
        if (editingItem) {
          result = await operationsHook.updateElement(editingItem.id, values);
        } else {
          result = await operationsHook.createElement(
            values as API.CreateHistoricalElementParams,
          );
        }

        if (result.success) {
          setModalVisible(false);
          const { searchKeyword, regionFilter, typeFilter } = filtersHook;
          dataHook.fetchData(
            dataHook.pagination.current,
            dataHook.pagination.pageSize,
            searchKeyword,
            regionFilter,
            typeFilter,
          );
        }
      },
      [editingItem, operationsHook, filtersHook, dataHook],
    );

    const handleModalCancel = useCallback(() => {
      setModalVisible(false);
      setEditingItem(null);
    }, []);

    // 搜索筛选操作
    const handleSearch = useCallback(
      (value: string) => {
        filtersHook.handleSearch(value);
      },
      [filtersHook],
    );

    const handleRegionFilterChange = useCallback(
      (value: number | undefined) => {
        filtersHook.handleRegionFilterChange(value);
      },
      [filtersHook],
    );

    const handleTypeFilterChange = useCallback(
      (value: number | undefined) => {
        filtersHook.handleTypeFilterChange(value);
      },
      [filtersHook],
    );

    const handleRefresh = useCallback(() => {
      const { searchKeyword, regionFilter, typeFilter } = filtersHook;
      dataHook.fetchData(
        dataHook.pagination.current,
        dataHook.pagination.pageSize,
        searchKeyword,
        regionFilter,
        typeFilter,
      );
    }, [filtersHook, dataHook]);

    const handleReset = useCallback(() => {
      filtersHook.resetFilters();
    }, [filtersHook]);

    // 批量操作
    const handleBatchDelete = useCallback(async () => {
      if (!filtersHook.checkSelection()) {
        message.warning('请选择要操作的记录');
        return;
      }

      const selectedIds = filtersHook.getSelectedIds();
      const result = await operationsHook.batchDeleteElements(selectedIds);

      if (result.success) {
        filtersHook.clearSelection();
        const { searchKeyword, regionFilter, typeFilter } = filtersHook;
        dataHook.fetchData(
          dataHook.pagination.current,
          dataHook.pagination.pageSize,
          searchKeyword,
          regionFilter,
          typeFilter,
        );
      }
    }, [filtersHook, operationsHook, dataHook]);

    const handleImportModalOpen = useCallback(() => {
      setImportModalVisible(true);
    }, []);

    const handleImportModalClose = useCallback(() => {
      setImportModalVisible(false);
    }, []);

    const handleBatchImport = useCallback(
      async (importData: string) => {
        if (!importData || importData.trim() === '') {
          message.error('请输入导入数据');
          return;
        }

        // 解析JSON数据
        let elements;
        try {
          elements = JSON.parse(importData);
          if (!Array.isArray(elements)) {
            throw new Error('数据格式错误，应为数组格式');
          }
        } catch (parseError) {
          message.error('JSON格式错误，请检查数据格式');
          return;
        }

        const result = await operationsHook.batchImportElements(elements);

        if (result.success) {
          handleImportModalClose();
          dataHook.fetchData(1, dataHook.pagination.pageSize);
        }
      },
      [operationsHook, dataHook],
    );

    // Excel导入相关函数
    const handleDownloadTemplate = useCallback(async () => {
      await operationsHook.downloadTemplate();
    }, [operationsHook]);

    const handlePreviewExcel = useCallback(
      async (file: File) => {
        return await operationsHook.previewExcelImport(file);
      },
      [operationsHook],
    );

    const handleImportExcel = useCallback(
      async (file: File) => {
        const result = await operationsHook.importExcelFile(file);
        if (result.success) {
          handleImportModalClose();
          dataHook.fetchData(1, dataHook.pagination.pageSize);
        }
        return result;
      },
      [operationsHook, dataHook],
    );

    // 分页操作
    const handlePaginationChange = useCallback(
      (page: number, pageSize: number) => {
        const { searchKeyword, regionFilter, typeFilter } = filtersHook;
        dataHook.fetchData(
          page,
          pageSize,
          searchKeyword,
          regionFilter,
          typeFilter,
        );
      },
      [filtersHook, dataHook],
    );

    const handleShowSizeChange = useCallback(
      (_current: number, size: number) => {
        const { searchKeyword, regionFilter, typeFilter } = filtersHook;
        dataHook.fetchData(1, size, searchKeyword, regionFilter, typeFilter);
      },
      [filtersHook, dataHook],
    );

    // 标签页操作
    const handleTabChange = useCallback((key: string) => {
      setActiveTab(key);
    }, []);

    return {
      // 数据状态
      data: dataHook.data,
      statistics: dataHook.statistics,
      timelineData: dataHook.timelineData,

      // 加载状态
      loading: dataHook.loading,
      statisticsLoading: dataHook.statisticsLoading,
      timelineLoading: dataHook.timelineLoading,
      operationLoading: operationsHook.operationLoading,
      batchLoading: operationsHook.batchLoading,

      // 分页状态
      pagination: dataHook.pagination,

      // 筛选状态
      searchKeyword: filtersHook.searchKeyword,
      regionFilter: filtersHook.regionFilter,
      typeFilter: filtersHook.typeFilter,
      selectedRowKeys: filtersHook.selectedRowKeys,

      // 表单状态
      modalVisible,
      editingItem,
      importModalVisible,

      // 标签页状态
      activeTab,

      // 操作函数
      handleAdd,
      handleEdit,
      handleDelete,
      handleSubmit,
      handleModalCancel,

      // 搜索筛选函数
      handleSearch,
      handleRegionFilterChange,
      handleTypeFilterChange,
      handleRefresh,
      handleReset,

      // 批量操作函数
      handleSelectChange: filtersHook.handleSelectChange,
      clearSelection: filtersHook.clearSelection,
      handleBatchDelete,
      handleImportModalOpen,
      handleImportModalClose,
      handleBatchImport,

      // 分页函数
      handlePaginationChange,
      handleShowSizeChange,

      // 标签页函数
      handleTabChange,

      // Excel导入函数
      handleDownloadTemplate,
      handlePreviewExcel,
      handleImportExcel,
    };
  };
