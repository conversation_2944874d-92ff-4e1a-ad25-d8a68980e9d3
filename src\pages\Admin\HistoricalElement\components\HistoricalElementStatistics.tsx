import type { HistoricalElementStatistics as StatisticsType } from '@/services/historicalElement';
import { BarChartOutlined, EnvironmentOutlined, HistoryOutlined, PieChartOutlined } from '@ant-design/icons';
import { Card, Col, Row, Statistic } from 'antd';
import ReactECharts from 'echarts-for-react';
import React, { useMemo } from 'react';

export interface HistoricalElementStatisticsProps {
  statistics: StatisticsType | null;
  loading: boolean;
}

export const HistoricalElementStatistics: React.FC<
  HistoricalElementStatisticsProps
> = ({ statistics, loading }) => {
  // 生成图表配置
  const chartOptions = useMemo(() => {
    if (!statistics) return { pieOption: null, barOption: null, timelineOption: null };

    // 饼图配置 - 按类型统计
    const pieOption = {
      title: {
        text: '类型分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
      },
      series: [
        {
          name: '类型统计',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: statistics.byType.map((item, index) => ({
            value: item.count,
            name: item.typeName,
            itemStyle: {
              color: ['#1890ff', '#52c41a', '#fa8c16', '#722ed1', '#eb2f96'][index % 5],
            },
          })),
        },
      ],
    };

    // 柱状图配置 - 按区域统计
    const barOption = {
      title: {
        text: '区域分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: statistics.byRegion.map(item => item.regionName),
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0],
          },
          data: statistics.byRegion.map(item => item.count),
        },
      ],
    };

    // 时间轴配置 - 按时期统计
    const timelineOption = {
      title: {
        text: '时期分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: statistics.byPeriod.map(item => item.period),
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '数量',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            color: '#52c41a',
            width: 3,
          },
          itemStyle: {
            color: '#52c41a',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(82, 196, 26, 0.3)',
                },
                {
                  offset: 1,
                  color: 'rgba(82, 196, 26, 0.1)',
                },
              ],
            },
          },
          data: statistics.byPeriod.map(item => item.count),
        },
      ],
    };

    return { pieOption, barOption, timelineOption };
  }, [statistics]);

  // 渲染统计卡片
  const renderStatisticsCards = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总数量"
              value={statistics.total}
              prefix={<HistoryOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="类型数量"
              value={statistics.byType.length}
              prefix={<PieChartOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="区域数量"
              value={statistics.byRegion.length}
              prefix={<EnvironmentOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="时期数量"
              value={statistics.byPeriod.length}
              prefix={<BarChartOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染图表区域
  const renderCharts = () => {
    if (!statistics || !chartOptions.pieOption) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.pieOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.barOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.timelineOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染统计详情表格
  const renderStatisticsDetails = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16}>
        <Col span={8}>
          <Card title="按类型统计" loading={loading}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {statistics.byType.map((item, index) => (
                <div
                  key={item.typeId}
                  style={{
                    marginBottom: 12,
                    padding: '8px 12px',
                    backgroundColor: index % 2 === 0 ? '#fafafa' : '#fff',
                    borderRadius: 4,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <span style={{ color: '#666' }}>{item.typeName}</span>
                  <span style={{
                    fontWeight: 'bold',
                    color: ['#1890ff', '#52c41a', '#fa8c16', '#722ed1', '#eb2f96'][index % 5],
                    fontSize: '16px'
                  }}>
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="按区域统计" loading={loading}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {statistics.byRegion.map((item, index) => (
                <div
                  key={item.regionId}
                  style={{
                    marginBottom: 12,
                    padding: '8px 12px',
                    backgroundColor: index % 2 === 0 ? '#fafafa' : '#fff',
                    borderRadius: 4,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <span style={{ color: '#666' }}>{item.regionName}</span>
                  <span style={{
                    fontWeight: 'bold',
                    color: '#1890ff',
                    fontSize: '16px'
                  }}>
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="按时期统计" loading={loading}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {statistics.byPeriod.map((item, index) => (
                <div
                  key={item.period}
                  style={{
                    marginBottom: 12,
                    padding: '8px 12px',
                    backgroundColor: index % 2 === 0 ? '#fafafa' : '#fff',
                    borderRadius: 4,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <span style={{ color: '#666' }}>{item.period}</span>
                  <span style={{
                    fontWeight: 'bold',
                    color: '#52c41a',
                    fontSize: '16px'
                  }}>
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div>
      {renderStatisticsCards()}
      {renderCharts()}
      {renderStatisticsDetails()}
    </div>
  );
};
